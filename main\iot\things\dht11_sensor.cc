#include "iot/thing.h"
#include "settings.h"
#include <dht.h>
#include <esp_timer.h>

#include <driver/gpio.h>
#include <esp_log.h>

#define TAG "DHT11Sensor"

namespace iot {

// DHT11温湿度传感器Thing类实现
class DHT11Sensor : public Thing {
private:
#ifdef CONFIG_IDF_TARGET_ESP32
    gpio_num_t gpio_num_ = GPIO_NUM_4;  // ESP32默认使用GPIO4
#else
    gpio_num_t gpio_num_ = GPIO_NUM_1;  // ESP32S3默认使用GPIO1
#endif
    esp_timer_handle_t read_timer_ = nullptr;  // 定时读取传感器数据的定时器
    float temperature_ = 0.0f;  // 缓存的温度值(摄氏度)
    float humidity_ = 0.0f;     // 缓存的湿度值(百分比)
    bool data_valid_ = false;   // 数据有效性标志

    // 初始化GPIO配置
    void InitializeGpio() {
        gpio_config_t config = {
            .pin_bit_mask = (1ULL << gpio_num_),
            .mode = GPIO_MODE_INPUT_OUTPUT_OD,  // DHT11需要开漏输出模式
            .pull_up_en = GPIO_PULLUP_ENABLE,   // 启用内部上拉电阻
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE,
        };
        ESP_ERROR_CHECK(gpio_config(&config));
        ESP_LOGI(TAG, "DHT11传感器GPIO%d初始化完成", gpio_num_);
    }

    // 读取传感器数据的回调函数
    static void ReadSensorCallback(void* arg) {
        DHT11Sensor* sensor = static_cast<DHT11Sensor*>(arg);
        sensor->ReadSensorData();
    }

    // 读取传感器数据
    void ReadSensorData() {
        float temp, hum;
        esp_err_t result = dht_read_float_data(DHT_TYPE_DHT11, gpio_num_, &hum, &temp);
        
        if (result == ESP_OK) {
            temperature_ = temp;
            humidity_ = hum;
            data_valid_ = true;
            ESP_LOGD(TAG, "DHT11读取成功: 温度=%.1f°C, 湿度=%.1f%%", temp, hum);
        } else {
            ESP_LOGW(TAG, "DHT11读取失败: %s", esp_err_to_name(result));
            // 读取失败时保持上次有效值，不更新data_valid_标志
        }
    }

public:
    DHT11Sensor() : Thing("DHT11Sensor", "DHT11温湿度传感器") {
        // 从Settings获取自定义GPIO配置
        Settings settings("dht11", false);
        gpio_num_ = (gpio_num_t)settings.GetInt("gpio", (int)gpio_num_);
        
        // 初始化GPIO
        InitializeGpio();
        
        // 创建定时器，每2秒读取一次传感器数据
        esp_timer_create_args_t timer_args = {
            .callback = ReadSensorCallback,
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "dht11_read_timer",
            .skip_unhandled_events = true
        };
        ESP_ERROR_CHECK(esp_timer_create(&timer_args, &read_timer_));
        ESP_ERROR_CHECK(esp_timer_start_periodic(read_timer_, 2000000)); // 2秒间隔
        
        ESP_LOGI(TAG, "DHT11传感器初始化完成，GPIO=%d", gpio_num_);
    }

    ~DHT11Sensor() {
        // 清理定时器资源
        if (read_timer_ != nullptr) {
            esp_timer_stop(read_timer_);
            esp_timer_delete(read_timer_);
            read_timer_ = nullptr;
        }
        ESP_LOGI(TAG, "DHT11传感器资源已释放");
    }
};

} // namespace iot

DECLARE_THING(DHT11Sensor);
