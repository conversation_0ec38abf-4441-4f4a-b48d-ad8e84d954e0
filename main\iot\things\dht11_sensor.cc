#include "iot/thing.h"
#include "settings.h"
#include <dht.h>
#include <esp_timer.h>

#include <driver/gpio.h>
#include <esp_log.h>

#define TAG "DHT11Sensor"

namespace iot {

// DHT11温湿度传感器Thing类实现
class DHT11Sensor : public Thing {
private:
#ifdef CONFIG_IDF_TARGET_ESP32
    gpio_num_t gpio_num_ = GPIO_NUM_4;  // ESP32默认使用GPIO4
#else
    gpio_num_t gpio_num_ = GPIO_NUM_1;  // ESP32S3默认使用GPIO1
#endif
    esp_timer_handle_t read_timer_ = nullptr;  // 定时读取传感器数据的定时器
    float temperature_ = 0.0f;  // 缓存的温度值(摄氏度)
    float humidity_ = 0.0f;     // 缓存的湿度值(百分比)
    bool data_valid_ = false;   // 数据有效性标志
    uint32_t read_count_ = 0;   // 读取次数计数
    uint32_t error_count_ = 0;  // 错误次数计数
    int64_t last_read_time_ = 0; // 上次读取时间戳

    // 验证GPIO配置有效性
    bool IsValidGpio(gpio_num_t gpio) {
        return (gpio >= GPIO_NUM_0 && gpio < GPIO_NUM_MAX && GPIO_IS_VALID_GPIO(gpio));
    }

    // 初始化GPIO配置
    void InitializeGpio() {
        // 验证GPIO有效性
        if (!IsValidGpio(gpio_num_)) {
            ESP_LOGE(TAG, "无效的GPIO引脚: %d，使用默认配置", gpio_num_);
#ifdef CONFIG_IDF_TARGET_ESP32
            gpio_num_ = GPIO_NUM_4;  // 回退到ESP32默认GPIO
#else
            gpio_num_ = GPIO_NUM_1;  // 回退到ESP32S3默认GPIO
#endif
        }

        gpio_config_t config = {
            .pin_bit_mask = (1ULL << gpio_num_),
            .mode = GPIO_MODE_INPUT_OUTPUT_OD,  // DHT11需要开漏输出模式
            .pull_up_en = GPIO_PULLUP_ENABLE,   // 启用内部上拉电阻
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE,
        };

        esp_err_t ret = gpio_config(&config);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "GPIO%d配置失败: %s", gpio_num_, esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "DHT11传感器GPIO%d初始化完成", gpio_num_);
        }
    }

    // 读取传感器数据的回调函数
    static void ReadSensorCallback(void* arg) {
        DHT11Sensor* sensor = static_cast<DHT11Sensor*>(arg);
        sensor->ReadSensorData();
    }

    // 验证传感器数据有效性
    bool IsValidSensorData(float temp, float hum) {
        // DHT11温度范围: 0-50°C, 湿度范围: 20-90%
        return (temp >= -10.0f && temp <= 60.0f && hum >= 0.0f && hum <= 100.0f);
    }

    // 读取传感器数据
    void ReadSensorData() {
        int64_t current_time = esp_timer_get_time();
        read_count_++;

        float temp, hum;
        esp_err_t result = dht_read_float_data(DHT_TYPE_DHT11, gpio_num_, &hum, &temp);

        if (result == ESP_OK) {
            // 验证数据有效性
            if (IsValidSensorData(temp, hum)) {
                temperature_ = temp;
                humidity_ = hum;
                data_valid_ = true;
                last_read_time_ = current_time;
                ESP_LOGD(TAG, "DHT11读取成功: 温度=%.1f°C, 湿度=%.1f%% (读取次数: %lu)",
                         temp, hum, read_count_);
            } else {
                error_count_++;
                ESP_LOGW(TAG, "DHT11数据异常: 温度=%.1f°C, 湿度=%.1f%% (忽略)", temp, hum);
            }
        } else {
            error_count_++;
            ESP_LOGW(TAG, "DHT11读取失败: %s (错误次数: %lu/%lu)",
                     esp_err_to_name(result), error_count_, read_count_);

            // 连续错误过多时标记数据无效
            if (error_count_ > 10 && (error_count_ * 100 / read_count_) > 50) {
                data_valid_ = false;
                ESP_LOGE(TAG, "DHT11传感器错误率过高，标记数据无效");
            }
        }
    }

public:
    DHT11Sensor() : Thing("DHT11Sensor", "DHT11温湿度传感器") {
        // 从Settings获取自定义GPIO配置，使用统一的配置键管理
        Settings settings("dht11", false);
        int custom_gpio = settings.GetInt("gpio", (int)gpio_num_);

        // 验证并设置GPIO配置
        if (custom_gpio != (int)gpio_num_) {
            ESP_LOGI(TAG, "使用自定义GPIO配置: %d (默认: %d)", custom_gpio, gpio_num_);
            gpio_num_ = (gpio_num_t)custom_gpio;
        } else {
            ESP_LOGI(TAG, "使用默认GPIO配置: %d", gpio_num_);
        }

        // 初始化GPIO
        InitializeGpio();
        
        // 创建定时器，每2秒读取一次传感器数据
        esp_timer_create_args_t timer_args = {
            .callback = ReadSensorCallback,
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "dht11_read_timer",
            .skip_unhandled_events = true
        };

        esp_err_t ret = esp_timer_create(&timer_args, &read_timer_);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "创建定时器失败: %s", esp_err_to_name(ret));
            return;
        }

        ret = esp_timer_start_periodic(read_timer_, 2000000); // 2秒间隔
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "启动定时器失败: %s", esp_err_to_name(ret));
            esp_timer_delete(read_timer_);
            read_timer_ = nullptr;
            return;
        }

        // 定义设备的属性 - 温度和湿度数据
        properties_.AddNumberProperty("temperature", "当前温度(°C)", [this]() -> int {
            if (data_valid_) {
                return static_cast<int>(temperature_); // 返回整数温度值
            }
            return 0; // 数据无效时返回0°C
        });

        properties_.AddNumberProperty("humidity", "当前湿度(%)", [this]() -> int {
            if (data_valid_) {
                return static_cast<int>(humidity_); // 返回整数湿度值
            }
            return 0; // 数据无效时返回0%
        });

        ESP_LOGI(TAG, "DHT11传感器初始化完成，GPIO=%d，定时器间隔2秒", gpio_num_);
    }

    ~DHT11Sensor() {
        // 清理定时器资源
        if (read_timer_ != nullptr) {
            esp_err_t ret = esp_timer_stop(read_timer_);
            if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
                ESP_LOGW(TAG, "停止定时器失败: %s", esp_err_to_name(ret));
            }

            ret = esp_timer_delete(read_timer_);
            if (ret != ESP_OK) {
                ESP_LOGW(TAG, "删除定时器失败: %s", esp_err_to_name(ret));
            }
            read_timer_ = nullptr;
        }

        ESP_LOGI(TAG, "DHT11传感器资源已释放 (读取次数: %lu, 错误次数: %lu)",
                 read_count_, error_count_);
    }
};

} // namespace iot

DECLARE_THING(DHT11Sensor);
